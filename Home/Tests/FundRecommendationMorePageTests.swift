//
//  FundRecommendationMorePageTests.swift
//  HomeTests
//
//  Created by Augment Agent on 26/08/2025.
//

import XCTest
@testable import Home

class FundRecommendationMorePageTests: XCTestCase {

    // MARK: - Test Data
    
    private func createMockFundRecommendationCategory() -> FundRecommendationCategory {
        let instruments1 = [
            FundRecommendationInstrument(
                instrumentId: 1, instrumentName: "Fund A", symbol: "FUNDA", 
                exchange: "NYSE", currency: "USD", logo: nil, price: nil, 
                priceChange: nil, priceChangeRate: "5.0", allocationClass: "Equity", 
                riskLevel: "Medium", period: "1Y"
            )
        ]
        
        let instruments2 = [
            FundRecommendationInstrument(
                instrumentId: 2, instrumentName: "Fund B", symbol: "FUNDB", 
                exchange: "NASDAQ", currency: "USD", logo: nil, price: nil, 
                priceChange: nil, priceChangeRate: "3.5", allocationClass: "Bond", 
                riskLevel: "Low", period: "2Y"
            )
        ]
        
        let instruments3 = [
            FundRecommendationInstrument(
                instrumentId: 3, instrumentName: "Fund C", symbol: "FUNDC", 
                exchange: "LSE", currency: "GBP", logo: nil, price: nil, 
                priceChange: nil, priceChangeRate: "7.2", allocationClass: "Mixed", 
                riskLevel: "High", period: "3Y"
            )
        ]
        
        let children = [
            FundRecommendationChild(categoryId: 0, categoryName: "Equity Funds", instruments: instruments1),
            FundRecommendationChild(categoryId: 1, categoryName: "Bond Funds", instruments: instruments2),
            FundRecommendationChild(categoryId: 2, categoryName: "Mixed Funds", instruments: instruments3)
        ]
        
        return FundRecommendationCategory(
            categoryId: 1,
            categoryName: "Investment Funds",
            children: children,
            instruments: []
        )
    }

    // MARK: - Tests

    func testMorePageCategoryStateSynchronization() {
        // Given: A fund recommendation category with 3 children and selected index 1
        let mockCategory = createMockFundRecommendationCategory()
        let selectedIndex = 1
        
        // When: Creating the More page view model with the selected category index
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Investment Funds",
            selectedCategoryIndex: selectedIndex
        )
        
        // Then: The view model should preserve the selected category index
        let expectation = XCTestExpectation(description: "Selected category index should be preserved")
        
        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: Observable.empty()
        )
        
        let output = viewModel.transform(input: input)
        
        output.selectedCategoryIndex
            .drive(onNext: { index in
                XCTAssertEqual(index, selectedIndex, "Selected category index should match the initial value from home page")
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())
        
        wait(for: [expectation], timeout: 1.0)
    }

    func testMorePageDisplaysCorrectInstrumentsForSelectedCategory() {
        // Given: A fund recommendation category with different instruments per category
        let mockCategory = createMockFundRecommendationCategory()
        let selectedIndex = 1 // Bond Funds
        
        // When: Creating the More page view model
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Investment Funds",
            selectedCategoryIndex: selectedIndex
        )
        
        // Then: The view model should display instruments for the selected category
        let expectation = XCTestExpectation(description: "Should display instruments for selected category")
        
        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: Observable.empty()
        )
        
        let output = viewModel.transform(input: input)
        
        output.instruments
            .drive(onNext: { instruments in
                XCTAssertEqual(instruments.count, 1, "Should display instruments from selected category")
                XCTAssertEqual(instruments[0].instrumentName, "Fund B", "Should display Fund B from Bond Funds category")
                XCTAssertEqual(instruments[0].allocationClass, "Bond", "Should display bond allocation class")
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())
        
        wait(for: [expectation], timeout: 1.0)
    }

    func testMorePageCategoryButtonsCreation() {
        // Given: A fund recommendation category with 3 children
        let mockCategory = createMockFundRecommendationCategory()
        
        // When: Creating the More page view model
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Investment Funds",
            selectedCategoryIndex: 0
        )
        
        // Then: The view model should provide categories for button creation
        let expectation = XCTestExpectation(description: "Should provide categories for buttons")
        
        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: Observable.empty()
        )
        
        let output = viewModel.transform(input: input)
        
        output.categories
            .drive(onNext: { categories in
                XCTAssertEqual(categories.count, 3, "Should provide all 3 categories for button creation")
                XCTAssertEqual(categories[0].categoryName, "Equity Funds", "First category should be Equity Funds")
                XCTAssertEqual(categories[1].categoryName, "Bond Funds", "Second category should be Bond Funds")
                XCTAssertEqual(categories[2].categoryName, "Mixed Funds", "Third category should be Mixed Funds")
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())
        
        wait(for: [expectation], timeout: 1.0)
    }

    func testMorePageCategorySelectionUpdatesInstruments() {
        // Given: A fund recommendation category with different instruments per category
        let mockCategory = createMockFundRecommendationCategory()
        
        // When: Creating the More page view model and changing category selection
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Investment Funds",
            selectedCategoryIndex: 0
        )
        
        let categorySelectionSubject = PublishSubject<Int>()
        
        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: categorySelectionSubject.asObservable()
        )
        
        let output = viewModel.transform(input: input)
        
        // Then: Instruments should update when category selection changes
        let expectation = XCTestExpectation(description: "Instruments should update when category changes")
        expectation.expectedFulfillmentCount = 2
        
        var instrumentResults: [[FundRecommendationInstrument]] = []
        
        output.instruments
            .drive(onNext: { instruments in
                instrumentResults.append(instruments)
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())
        
        // Trigger category selection change from Equity Funds (0) to Mixed Funds (2)
        categorySelectionSubject.onNext(2)
        
        wait(for: [expectation], timeout: 1.0)
        
        XCTAssertEqual(instrumentResults.count, 2, "Should receive instruments for both categories")
        XCTAssertEqual(instrumentResults[0][0].instrumentName, "Fund A", "First category should show Fund A (Equity)")
        XCTAssertEqual(instrumentResults[1][0].instrumentName, "Fund C", "Second category should show Fund C (Mixed)")
    }
}
