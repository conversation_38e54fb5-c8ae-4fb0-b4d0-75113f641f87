//
//  FundRecommendationStateTests.swift
//  HomeTests
//
//  Created by Augment Agent on 25/08/2025.
//

import XCTest

@testable import Home

class FundRecommendationStateTests: XCTestCase {

    // MARK: - Test Data

    private func createMockFundRecommendationCategory(childrenCount: Int = 3)
        -> FundRecommendationCategory
    {
        let children = (0..<childrenCount).map { index in
            FundRecommendationChild(
                categoryId: index,
                categoryName: "Category \(index)",
                instruments: []
            )
        }

        return FundRecommendationCategory(
            categoryId: 1,
            categoryName: "Test Category",
            children: children,
            instruments: []
        )
    }

    // MARK: - Tests

    func testCategorySelectionStateSynchronization() {
        // Given
        let mockCategory = createMockFundRecommendationCategory(childrenCount: 3)
        let selectedIndex = 1

        // When creating view model with selected category index
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Test Category",
            selectedCategoryIndex: selectedIndex
        )

        // Then the selected category index should be preserved
        let expectation = XCTestExpectation(description: "Selected category index should be preserved")

        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: Observable.empty()
        )

        let output = viewModel.transform(input: input)

        output.selectedCategoryIndex
            .drive(onNext: { index in
                XCTAssertEqual(index, selectedIndex, "Selected category index should match the initial value")
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())

        wait(for: [expectation], timeout: 1.0)
    }

    func testCategoryButtonsCreation() {
        // Given
        let mockCategory = createMockFundRecommendationCategory(childrenCount: 3)

        // When creating view model
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Test Category",
            selectedCategoryIndex: 0
        )

        // Then categories should be provided for button creation
        let expectation = XCTestExpectation(description: "Categories should be provided")

        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: Observable.empty()
        )

        let output = viewModel.transform(input: input)

        output.categories
            .drive(onNext: { categories in
                XCTAssertEqual(categories.count, 3, "Should provide all 3 categories for buttons")
                XCTAssertEqual(categories[0].categoryName, "Category 0", "First category name should match")
                XCTAssertEqual(categories[1].categoryName, "Category 1", "Second category name should match")
                XCTAssertEqual(categories[2].categoryName, "Category 2", "Third category name should match")
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())

        wait(for: [expectation], timeout: 1.0)
    }

    func testCategorySelectionUpdatesInstruments() {
        // Given
        let instruments1 = [createMockInstrument(name: "Fund A")]
        let instruments2 = [createMockInstrument(name: "Fund B")]

        let children = [
            FundRecommendationChild(categoryId: 0, categoryName: "Category 0", instruments: instruments1),
            FundRecommendationChild(categoryId: 1, categoryName: "Category 1", instruments: instruments2)
        ]

        let mockCategory = FundRecommendationCategory(
            categoryId: 1,
            categoryName: "Test Category",
            children: children,
            instruments: []
        )

        // When creating view model and selecting different category
        let viewModel = FundRecommendationListViewModel(
            router: UnownedRouter(HomeCoordinator(openSettings: {}, onboarding: { _ in })),
            fullCategory: mockCategory,
            mainCategoryName: "Test Category",
            selectedCategoryIndex: 0
        )

        let categorySelectionSubject = PublishSubject<Int>()

        let input = FundRecommendationListViewModel.Input(
            onViewAppear: Observable.just(()),
            categorySelection: categorySelectionSubject.asObservable()
        )

        let output = viewModel.transform(input: input)

        // Then instruments should update when category changes
        let expectation = XCTestExpectation(description: "Instruments should update when category changes")
        expectation.expectedFulfillmentCount = 2

        var instrumentResults: [[FundRecommendationInstrument]] = []

        output.instruments
            .drive(onNext: { instruments in
                instrumentResults.append(instruments)
                expectation.fulfill()
            })
            .disposed(by: DisposeBag())

        // Trigger category selection change
        categorySelectionSubject.onNext(1)

        wait(for: [expectation], timeout: 1.0)

        XCTAssertEqual(instrumentResults.count, 2, "Should receive instruments for both categories")
        XCTAssertEqual(instrumentResults[0][0].instrumentName, "Fund A", "First category should show Fund A")
        XCTAssertEqual(instrumentResults[1][0].instrumentName, "Fund B", "Second category should show Fund B")
    }

    // MARK: - Helper Methods

    private func createMockInstrument(name: String) -> FundRecommendationInstrument {
        return FundRecommendationInstrument(
            instrumentId: 1,
            instrumentName: name,
            symbol: "TEST",
            exchange: "TEST",
            currency: "USD",
            logo: nil,
            price: nil,
            priceChange: nil,
            priceChangeRate: "5.0",
            allocationClass: "Equity",
            riskLevel: "Medium",
            period: "1Y"
        )
    }

    func testCategoryIndexValidation() {
        let category = createMockFundRecommendationCategory(childrenCount: 3)

        // Test valid index
        let validIndex = 1
        let adjustedValidIndex = min(validIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedValidIndex, 1, "Valid index should remain unchanged")

        // Test index too high
        let highIndex = 5
        let adjustedHighIndex = min(highIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedHighIndex, 2, "High index should be clamped to max valid index")

        // Test negative index
        let negativeIndex = -1
        let adjustedNegativeIndex = min(negativeIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedNegativeIndex, -1, "Negative index should be handled by max(0, ...)")
        let finalIndex = max(0, adjustedNegativeIndex)
        XCTAssertEqual(finalIndex, 0, "Final index should be 0 for negative input")
    }

    func testEmptyChildrenHandling() {
        let category = createMockFundRecommendationCategory(childrenCount: 0)

        let selectedIndex = 1
        let adjustedIndex = min(selectedIndex, max(0, category.children.count - 1))
        XCTAssertEqual(adjustedIndex, 0, "Index should be 0 when no children exist")
    }

    func testStatePreservationLogic() {
        // Simulate the state preservation logic from HomeViewController
        var selectedFundCategoryIndex = 2
        let category = createMockFundRecommendationCategory(childrenCount: 3)

        // Validate index (simulating validateSelectedFundCategoryIndex method)
        let maxIndex = max(0, category.children.count - 1)
        selectedFundCategoryIndex = min(selectedFundCategoryIndex, maxIndex)

        XCTAssertEqual(selectedFundCategoryIndex, 2, "Valid index should be preserved")

        // Test with smaller category
        let smallerCategory = createMockFundRecommendationCategory(childrenCount: 2)
        let smallerMaxIndex = max(0, smallerCategory.children.count - 1)
        selectedFundCategoryIndex = min(selectedFundCategoryIndex, smallerMaxIndex)

        XCTAssertEqual(
            selectedFundCategoryIndex, 1, "Index should be adjusted to fit smaller category")
    }

    func testCellConfigurationWithPreservedIndex() {
        let category = createMockFundRecommendationCategory(childrenCount: 3)

        // Test default behavior (index 0)
        let defaultSelectedIndex = 0
        let configuredIndex1 = min(defaultSelectedIndex, max(0, category.children.count - 1))
        XCTAssertEqual(configuredIndex1, 0, "Default index should be 0")

        // Test preserved index
        let preservedIndex = 2
        let configuredIndex2 = min(preservedIndex, max(0, category.children.count - 1))
        XCTAssertEqual(configuredIndex2, 2, "Preserved index should be maintained")

        // Test out-of-bounds preserved index
        let outOfBoundsIndex = 5
        let configuredIndex3 = min(outOfBoundsIndex, max(0, category.children.count - 1))
        XCTAssertEqual(configuredIndex3, 2, "Out-of-bounds index should be clamped")
    }
}
