//
//  FundRecommendationZeroReturnStylingTests.swift
//  HomeTests
//
//  Created by Augment Agent on 26/08/2025.
//

import XCTest
@testable import Home
import CUIModule

final class FundRecommendationZeroReturnStylingTests: XCTestCase {

    // MARK: - Test Zero Return Rate Styling

    func testZeroReturnRateDisplaysGrayColor() {
        // Given: A fund instrument with zero return rate
        let instrument = FundRecommendationInstrument(
            instrumentId: 1,
            instrumentName: "Test Fund",
            symbol: "TEST",
            exchange: "NYSE",
            currency: "USD",
            logo: nil,
            price: "100.00",
            priceChange: "0.00",
            priceChangeRate: "0.00",
            allocationClass: "Equity",
            riskLevel: "Medium",
            period: "1Y"
        )
        
        // When: Configuring a fund item view with zero return rate
        let fundItemView = createMockFundItemView()
        fundItemView.configure(with: instrument)
        
        // Then: The return rate should be displayed in gray color
        XCTAssertEqual(fundItemView.priceChangeRateLabel.text, "0.00%")
        XCTAssertEqual(fundItemView.priceChangeRateLabel.textColor, Color.txtGray)
    }

    func testNilReturnRateDisplaysGrayColor() {
        // Given: A fund instrument with nil return rate
        let instrument = FundRecommendationInstrument(
            instrumentId: 1,
            instrumentName: "Test Fund",
            symbol: "TEST",
            exchange: "NYSE",
            currency: "USD",
            logo: nil,
            price: "100.00",
            priceChange: nil,
            priceChangeRate: nil,
            allocationClass: "Equity",
            riskLevel: "Medium",
            period: "1Y"
        )
        
        // When: Configuring a fund item view with nil return rate
        let fundItemView = createMockFundItemView()
        fundItemView.configure(with: instrument)
        
        // Then: The return rate should be displayed as "0.00%" in gray color
        XCTAssertEqual(fundItemView.priceChangeRateLabel.text, "0.00%")
        XCTAssertEqual(fundItemView.priceChangeRateLabel.textColor, Color.txtGray)
    }

    func testPositiveReturnRateDisplaysGreenColor() {
        // Given: A fund instrument with positive return rate
        let instrument = FundRecommendationInstrument(
            instrumentId: 1,
            instrumentName: "Test Fund",
            symbol: "TEST",
            exchange: "NYSE",
            currency: "USD",
            logo: nil,
            price: "100.00",
            priceChange: "5.00",
            priceChangeRate: "5.25",
            allocationClass: "Equity",
            riskLevel: "Medium",
            period: "1Y"
        )
        
        // When: Configuring a fund item view with positive return rate
        let fundItemView = createMockFundItemView()
        fundItemView.configure(with: instrument)
        
        // Then: The return rate should be displayed with "+" prefix in green color
        XCTAssertEqual(fundItemView.priceChangeRateLabel.text, "*****%")
        XCTAssertEqual(fundItemView.priceChangeRateLabel.textColor, Color.txtPositive)
    }

    func testNegativeReturnRateDisplaysRedColor() {
        // Given: A fund instrument with negative return rate
        let instrument = FundRecommendationInstrument(
            instrumentId: 1,
            instrumentName: "Test Fund",
            symbol: "TEST",
            exchange: "NYSE",
            currency: "USD",
            logo: nil,
            price: "100.00",
            priceChange: "-3.00",
            priceChangeRate: "-3.15",
            allocationClass: "Equity",
            riskLevel: "Medium",
            period: "1Y"
        )
        
        // When: Configuring a fund item view with negative return rate
        let fundItemView = createMockFundItemView()
        fundItemView.configure(with: instrument)
        
        // Then: The return rate should be displayed without "+" prefix in red color
        XCTAssertEqual(fundItemView.priceChangeRateLabel.text, "-3.15%")
        XCTAssertEqual(fundItemView.priceChangeRateLabel.textColor, Color.txtNegative)
    }

    // MARK: - Helper Methods

    private func createMockFundItemView() -> MockFundItemView {
        return MockFundItemView()
    }
}

// MARK: - Mock Fund Item View for Testing

private class MockFundItemView {
    let priceChangeRateLabel = UILabel()
    
    func configure(with instrument: FundRecommendationInstrument) {
        // Replicate the exact logic from the actual implementation
        if let priceChangeRate = instrument.priceChangeRate,
            let rateValue = Double(priceChangeRate)
        {
            let formattedRate = String(format: "%.2f", rateValue)
            let percentage = "\(formattedRate)%"

            if rateValue < 0 {
                priceChangeRateLabel.text = percentage
                priceChangeRateLabel.textColor = Color.txtNegative
            } else if rateValue == 0 {
                priceChangeRateLabel.text = percentage
                priceChangeRateLabel.textColor = Color.txtGray
            } else {
                priceChangeRateLabel.text = "+\(percentage)"
                priceChangeRateLabel.textColor = Color.txtPositive
            }
        } else {
            priceChangeRateLabel.text = "0.00%"
            priceChangeRateLabel.textColor = Color.txtGray
        }
    }
}
