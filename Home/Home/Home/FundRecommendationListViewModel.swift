//
//  FundRecommendationListViewModel.swift
//  Home
//
//  Created by Augment Agent on 25/08/2025.
//

import Core
import Foundation
import RxCocoa
import RxSwift
import SharedData
import XCoordinator

final class FundRecommendationListViewModel: AnyViewModel {

    // MARK: Properties
    private let router: UnownedRouter<HomeRoute>
    private let fullCategory: FundRecommendationCategory
    private let mainCategoryName: String
    private let selectedCategoryIndexSubject: BehaviorSubject<Int>
    private let disposeBag = DisposeBag()

    // MARK: - Reactive Properties
    private let categorySelectionSubject = PublishSubject<Int>()
    var categorySelection: Observable<Int> {
        return categorySelectionSubject.asObservable()
    }

    // MARK: Input/Output
    struct Input {
        let onViewAppear: Observable<Void>
        let categorySelection: Observable<Int>
    }

    struct Output {
        let title: Driver<String>
        let instruments: Driver<[FundRecommendationInstrument]>
        let categories: Driver<[FundRecommendationChild]>
        let selectedCategoryIndex: Driver<Int>
    }

    // MARK: Initialization
    init(
        router: UnownedRouter<HomeRoute>,
        fullCategory: FundRecommendationCategory,
        mainCategoryName: String,
        selectedCategoryIndex: Int = 0
    ) {
        self.router = router
        self.fullCategory = fullCategory
        self.mainCategoryName = mainCategoryName
        self.selectedCategoryIndexSubject = BehaviorSubject<Int>(value: selectedCategoryIndex)
    }

    // MARK: Transform
    func transform(input: Input) -> Output {
        // Create title using only the main category name
        let title = Observable.just(mainCategoryName)

        // Handle category selection
        input.categorySelection
            .subscribe(onNext: { [weak self] index in
                self?.selectedCategoryIndexSubject.onNext(index)
                self?.categorySelectionSubject.onNext(index)
            })
            .disposed(by: disposeBag)

        // Get instruments for the selected category
        let instruments = selectedCategoryIndexSubject
            .map { [weak self] selectedIndex -> [FundRecommendationInstrument] in
                guard let self = self,
                      selectedIndex < self.fullCategory.children.count else {
                    return []
                }
                return self.fullCategory.children[selectedIndex].instruments
            }

        // Provide first 3 categories for buttons
        let categories = Observable.just(Array(fullCategory.children.prefix(3)))

        return Output(
            title: title.asDriverOnErrorNever(),
            instruments: instruments.asDriverOnErrorNever(),
            categories: categories.asDriverOnErrorNever(),
            selectedCategoryIndex: selectedCategoryIndexSubject.asDriverOnErrorNever()
        )
    }

    // MARK: - Navigation

    func navigateToInstrumentDetail(_ fundInstrument: FundRecommendationInstrument) {
        let marketInstrument = convertToMarketInstrument(from: fundInstrument)
        router.trigger(.instrumentDetail(instrument: marketInstrument))
    }

    // MARK: - Utility Methods

    /// Converts FundRecommendationInstrument to MarketInstrument for navigation
    private func convertToMarketInstrument(from fundInstrument: FundRecommendationInstrument)
        -> MarketInstrument
    {
        return MarketInstrument(
            id: fundInstrument.instrumentId,
            symbol: fundInstrument.symbol,
            exchange: fundInstrument.exchange.isEmpty ? nil : fundInstrument.exchange,
            TimeZone: nil,
            market: nil,
            instrumentName: fundInstrument.instrumentName,
            currency: fundInstrument.currency.isEmpty ? nil : fundInstrument.currency,
            logo: fundInstrument.logo,
            riskLevel: fundInstrument.riskLevel,
            riskRating: nil,
            instrumentClass: nil,
            instrumentType: nil,
            instrumentCategory: nil,
            allocation: fundInstrument.allocationClass,
            assetClass: nil,
            lastPrice: fundInstrument.price,
            priceChange: fundInstrument.priceChange,
            priceChangePercentage: fundInstrument.priceChangeRate,
            favorite: nil,
            totalVolume: nil,
            totalAmount: nil
        )
    }
}
